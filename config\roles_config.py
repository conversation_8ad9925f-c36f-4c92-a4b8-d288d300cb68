# config/roles_config.py
from typing import TypedDict, Dict

class RoleInfo(TypedDict):
    name: str
    prompt_file: str
    voice: str
    # description: str # Пока не добавляем

ROLES_CONFIG: Dict[str, RoleInfo] = {
    "default": {
        "name": "По умолчанию",
        "prompt_file": "Default.txt",
        "voice": "alloy",
    },
    "emily": {
        "name": "Эми<PERSON><PERSON>",
        "prompt_file": "Emily.txt",
        "voice": "nova",
    },
    "isaac": {
        "name": "Айзек",
        "prompt_file": "Lsaac.txt", # Оставляем старое имя файла Lsaac.txt
        "voice": "echo",
    },
    "tutor": {
        "name": "Решебник",
        "prompt_file": "Tutor.txt",
        "voice": "fable",
    },
    "programmer": {
        "name": "Программист",
        "prompt_file": "Programmer.txt",
        "voice": "onyx",
    },
    "fitness": {
        "name": "Фитнес-тренер",
        "prompt_file": "Fitness.txt",
        "voice": "nova",
    }
    # Другие роли будут добавлены позже централизованно здесь
}

DEFAULT_ROLE_ID = "default"
