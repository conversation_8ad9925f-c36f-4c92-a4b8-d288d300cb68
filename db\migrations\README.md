# Скрипт для настройки базы данных

В папке `db` находится единый скрипт для полной настройки базы данных Supabase с нуля.

## Порядок установки

Для инициализации новой базы данных выполните следующие шаги:

1.  Войдите в вашу панель управления Supabase.
2.  Выберите проект и перейдите в раздел "SQL Editor".
3.  Откройте файл `db/setup_database.sql`, который находится в родительской директории.
4.  Скопируйте всё его содержимое, вставьте в редактор Supabase и выполните.

**ВАЖНО:** Выполняйте этот скрипт только на **чистой, новой** базе данных, чтобы избежать ошибок и потери данных.