import logging
import aiohttp
from typing import Optional, List, Dict, Any

from aiogram import Bot
from app.tools.base_tool import Tool
from app.supabase.users import get_user_language

logger = logging.getLogger(__name__)

class SearchEngineTool:
    """
    Инструмент для поиска в интернете через собственный экземпляр SearXNG.
    """
    name: str = "search_engine"
    description: str = "Searches the web using a private SearXNG instance. Use this to find information on the internet, answer questions about current events, or find resources on a specific topic."
    schema: dict = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query. Should be concise and in a natural language."
            },
            "language": {
                "type": "string",
                "description": "The language for the search (e.g., 'ru', 'en'). If not provided, the user's default language will be used."
            }
        },
        "required": ["query"]
    }

    SEARXNG_URL = "http://localhost:8080/search"

    async def execute(self, bot: Bot, chat_id: int, query: str, language: Optional[str] = None) -> str:
        """
        Выполняет веб-поиск и возвращает отформатированную строку с результатами.
        """
        if not language:
            language = await get_user_language(chat_id) or 'ru'  # По умолчанию 'ru', если язык не найден

        try:
            params = {
                "q": query,
                "format": "json",
                "language": language
            }

            logger.info(f"Выполняется поиск SearXNG с запросом: '{query}' для языка '{language}'")

            async with aiohttp.ClientSession() as session:
                async with session.get(self.SEARXNG_URL, params=params, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()

            return self._format_results(data, query)

        except aiohttp.ClientConnectorError:
            logger.error(f"Не удалось подключиться к экземпляру SearXNG по адресу {self.SEARXNG_URL}.")
            return "Ошибка: Не удалось подключиться к серверу поиска. Возможно, сервис недоступен."
        except aiohttp.ClientResponseError as e:
            logger.error(f"Сервер SearXNG вернул ошибку: {e.status} {e.message}")
            return f"Ошибка: Сервер поиска вернул ошибку ({e.status}). Проверьте логи сервера."
        except Exception as e:
            logger.error(f"Произошла непредвиденная ошибка во время веб-поиска: {e}", exc_info=True)
            return "Ошибка: Произошла непредвиденная ошибка во время выполнения поиска."

    def _format_results(self, data: Dict[str, Any], query: str) -> str:
        """
        Форматирует JSON-ответ от SearXNG в читаемую строку на русском языке.
        """
        results = data.get("results", [])
        if not results:
            return f"По вашему запросу '{query}' ничего не найдено."

        header = f"Результаты поиска по запросу '{query}':\n"
        formatted_results = [header]

        # Ограничиваемся 5 лучшими результатами
        for i, result in enumerate(results[:5], 1):
            title = result.get('title', "Без заголовка")
            url = result.get('url', '#')
            snippet = result.get('content', "Описание недоступно.").strip()

            link_text = "Ссылка"
            snippet_text = "Кратко"

            formatted_results.append(f"{i}. {title}\n   {link_text}: {url}\n   {snippet_text}: {snippet}\n")

        return "\n".join(formatted_results)