"""
Инструмент для отправки голосовых ответов.
Позволяет боту самостоятельно решать, когда использовать голосовые сообщения.
"""

import os
import tempfile
import logging
import aiofiles
from aiogram import Bot
from aiogram.types import BufferedInputFile
from aiogram.enums import ChatAction

from app.tools.base_tool import Tool
from app.voice_messages import text_to_speech

logger = logging.getLogger(__name__)


class VoiceResponseTool:
    """
    Инструмент для отправки голосовых ответов пользователям.
    Преобразует текст в речь и отправляет как голосовое сообщение.
    
    Реализует протокол Tool для интеграции с системой Tool Calling.
    """
    
    # Обязательные атрибуты протокола Tool
    name: str = "voice_response"
    description: str = "Sends a voice message to the user. Use when you want to reply with voice instead of text, based on context and content."
    schema: dict = {
        "type": "object",
        "properties": {
            "text": {
                "type": "string",
                "description": "The text to convert to voice message. Should be the complete response text."
            }
        },
        "required": ["text"]
    }
    
    async def execute(self, bot: Bot, chat_id: int, text: str) -> str:
        """
        Преобразует текст в голосовое сообщение и отправляет пользователю.
        
        Args:
            bot: Экземпляр Telegram бота
            chat_id: ID чата для отправки голосового сообщения
            text: Текст для преобразования в речь
            
        Returns:
            str: Результат выполнения для LLM
        """
        try:
            # Используем chat_id как user_id (в нашем случае они совпадают)
            user_id = chat_id
            
            # Создаем временный файл для аудио
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
                temp_audio_path = temp_file.name
            
            # Отправляем индикатор записи голоса
            await bot.send_chat_action(
                chat_id=chat_id,
                action=ChatAction.RECORD_VOICE
            )
            
            # Преобразуем текст в речь
            tts_success = await text_to_speech(text, temp_audio_path, user_id)
            
            if not tts_success:
                # Удаляем временный файл при ошибке
                try:
                    os.remove(temp_audio_path)
                except:
                    pass
                logger.error(f"Не удалось синтезировать речь для пользователя {user_id}")
                return "Failed to synthesize voice. The response was sent as text instead."
            
            # Отправляем индикатор загрузки голосового сообщения
            await bot.send_chat_action(
                chat_id=chat_id,
                action=ChatAction.UPLOAD_VOICE
            )
            
            # Читаем аудиофайл и отправляем
            async with aiofiles.open(temp_audio_path, "rb") as audio_file:
                audio_content = await audio_file.read()
                voice_response = BufferedInputFile(
                    audio_content,
                    filename="voice_response.mp3"
                )
            
            # Отправляем голосовое сообщение
            sent_message = await bot.send_voice(chat_id=chat_id, voice=voice_response)
            
            # Удаляем временный файл
            try:
                os.remove(temp_audio_path)
            except Exception as e:
                logger.error(f"Ошибка при удалении временного файла: {e}")
            
            logger.info(f"Голосовое сообщение успешно отправлено пользователю {user_id}")
            return "Voice message was successfully sent to the user."
            
        except Exception as e:
            logger.error(f"Ошибка при выполнении инструмента voice_response: {e}")
            
            # Пытаемся удалить временный файл в случае ошибки
            try:
                if 'temp_audio_path' in locals() and os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)
            except:
                pass
            
            return f"Failed to send voice message due to an error: {str(e)}"